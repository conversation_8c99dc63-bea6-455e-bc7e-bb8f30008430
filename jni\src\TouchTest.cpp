// 内核触摸接口测试文件
#include "Touch/KernelTouch.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace std;

// 测试内核触摸接口的基本功能
void TestKernelTouch() {
    cout << "开始测试内核触摸接口..." << endl;
    
    KernelTouch kernelTouch;
    
    // 测试初始化（假设屏幕分辨率为 1080x2340）
    int width = 1080;
    int height = 2340;
    
    if (!kernelTouch.Touch_Init(width, height)) {
        cout << "内核触摸初始化失败！" << endl;
        return;
    }
    
    cout << "内核触摸初始化成功！" << endl;
    cout << "屏幕尺寸: " << width << "x" << height << endl;
    
    // 测试单击事件
    cout << "测试单击事件..." << endl;
    int clickX = width / 2;
    int clickY = height / 2;
    
    kernelTouch.Touch_Down(0, clickX, clickY);
    this_thread::sleep_for(chrono::milliseconds(100));
    kernelTouch.Touch_Up(0);
    
    cout << "单击测试完成：(" << clickX << ", " << clickY << ")" << endl;
    
    // 测试滑动事件
    cout << "测试滑动事件..." << endl;
    int startX = width / 4;
    int startY = height / 2;
    int endX = width * 3 / 4;
    int endY = height / 2;
    
    kernelTouch.Touch_Down(0, startX, startY);
    
    // 分步滑动
    int steps = 10;
    for (int i = 1; i <= steps; i++) {
        int currentX = startX + (endX - startX) * i / steps;
        int currentY = startY + (endY - startY) * i / steps;
        
        kernelTouch.Touch_Move(0, currentX, currentY);
        this_thread::sleep_for(chrono::milliseconds(50));
    }
    
    kernelTouch.Touch_Up(0);
    cout << "滑动测试完成：从(" << startX << ", " << startY << ")到(" << endX << ", " << endY << ")" << endl;
    
    // 测试平滑移动功能
    cout << "测试平滑移动功能..." << endl;
    kernelTouch.Touch_Down(0, width / 2, height / 3);
    kernelTouch.SmoothMoveTo(width / 2, height * 2 / 3, 8);
    kernelTouch.Touch_Up(0);
    cout << "平滑移动测试完成" << endl;
    
    cout << "所有测试完成！" << endl;
}

// 模拟自瞄触摸测试
void TestAimTouch() {
    cout << "开始测试自瞄触摸..." << endl;
    
    KernelTouch kernelTouch;
    
    if (!kernelTouch.Touch_Init(1080, 2340)) {
        cout << "内核触摸初始化失败！" << endl;
        return;
    }
    
    // 模拟自瞄过程
    int centerX = 540;  // 屏幕中心X
    int centerY = 1170; // 屏幕中心Y
    
    // 开始自瞄触摸
    kernelTouch.Touch_Down(0, centerX, centerY);
    cout << "自瞄开始：触摸按下在(" << centerX << ", " << centerY << ")" << endl;
    
    // 模拟自瞄移动（向右上方移动）
    for (int i = 0; i < 20; i++) {
        int offsetX = i * 2;  // 每次移动2像素
        int offsetY = i * 1;  // 每次移动1像素
        
        kernelTouch.Touch_Move(0, centerX + offsetX, centerY - offsetY);
        this_thread::sleep_for(chrono::milliseconds(10)); // 添加延迟防止跳跃
    }
    
    // 结束自瞄
    kernelTouch.Touch_Up(0);
    cout << "自瞄结束：触摸抬起" << endl;
    
    cout << "自瞄触摸测试完成！" << endl;
}

int main() {
    cout << "=== 内核触摸接口测试程序 ===" << endl;
    
    // 基本功能测试
    TestKernelTouch();
    
    cout << endl;
    
    // 自瞄功能测试
    TestAimTouch();
    
    return 0;
}
