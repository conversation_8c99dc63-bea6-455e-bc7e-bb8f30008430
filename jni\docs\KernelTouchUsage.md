# 内核触摸接口使用说明

## 概述

本项目已将原有的用户空间模拟触摸系统替换为内核级触摸接口，提供更高的性能和更好的反检测能力。

## 主要改进

### 1. 性能提升
- **内核级操作**：直接在内核层面处理触摸事件，减少用户空间与内核空间的切换开销
- **硬件级模拟**：模拟真实手指触摸，具有更好的兼容性
- **高效随机数**：使用静态随机池，提供百万级别的高质量随机坐标

### 2. 反检测能力
- **内核层隐藏**：触摸事件在内核层生成，难以被用户空间检测
- **真实手指模拟**：完全模拟真实手指的触摸特性
- **随机化坐标**：内置高质量随机数生成器，避免机械化操作

### 3. 稳定性改进
- **防卡屏机制**：自动管理触摸点的按下和抬起，避免卡屏问题
- **边界检查**：自动限制坐标在有效范围内
- **平滑移动**：内置平滑移动算法，避免跳跃式移动

## 接口说明

### 基本接口

```cpp
// 初始化触摸系统
bool Touch_Init(int width, int height)

// 触摸按下
void Touch_Down(int id, int x, int y)

// 触摸移动
void Touch_Move(int id, int x, int y)

// 触摸抬起
void Touch_Up(int id)
```

### 高级功能

```cpp
// 平滑移动到目标位置（用于自瞄）
void SmoothMoveTo(int targetX, int targetY, int steps = 5)

// 获取高质量随机坐标
int GetRandomCoordinate(int maxValue)

// 检查初始化状态
bool IsInitialized() const

// 检查触摸状态
bool IsTouchDown() const
```

## 使用示例

### 1. 基本初始化

```cpp
#include "Touch/KernelTouch.h"

KernelTouch kernelTouch;

// 初始化（1080x2340分辨率）
if (!kernelTouch.Touch_Init(1080, 2340)) {
    // 初始化失败处理
    return false;
}
```

### 2. 单击操作

```cpp
// 单击屏幕中心
int centerX = 540;
int centerY = 1170;

kernelTouch.Touch_Down(0, centerX, centerY);
std::this_thread::sleep_for(std::chrono::milliseconds(100));
kernelTouch.Touch_Up(0);
```

### 3. 滑动操作

```cpp
// 从左向右滑动
int startX = 200, startY = 1000;
int endX = 800, endY = 1000;

kernelTouch.Touch_Down(0, startX, startY);

// 分步移动
for (int i = 1; i <= 10; i++) {
    int currentX = startX + (endX - startX) * i / 10;
    kernelTouch.Touch_Move(0, currentX, startY);
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
}

kernelTouch.Touch_Up(0);
```

### 4. 自瞄应用

```cpp
// 自瞄触摸移动
void AimMove(int targetX, int targetY) {
    if (!kernelTouch.IsTouchDown()) {
        // 开始自瞄
        kernelTouch.Touch_Down(0, screenCenterX, screenCenterY);
    }
    
    // 平滑移动到目标位置
    kernelTouch.SmoothMoveTo(targetX, targetY, 3);
}

// 结束自瞄
void AimEnd() {
    if (kernelTouch.IsTouchDown()) {
        kernelTouch.Touch_Up(0);
    }
}
```

## 重要注意事项

### 1. 坐标系统
- 坐标范围：`0 < x < width, 0 < y < height`
- 竖屏模式：width=1080, height=2340
- 横屏模式：width=2340, height=1080

### 2. 触摸管理
- **必须配对**：每个 `Touch_Down` 必须对应一个 `Touch_Up`
- **防止卡屏**：长时间不调用 `Touch_Up` 会导致卡屏
- **单点触控**：当前版本主要支持单点触控

### 3. 性能优化
- **添加延迟**：在 `Touch_Move` 后添加适当延迟（建议10ms）
- **平滑移动**：使用 `SmoothMoveTo` 而不是直接跳跃
- **边界检查**：系统会自动限制坐标在有效范围内

### 4. 错误处理
- 检查 `Touch_Init` 的返回值
- 使用 `IsInitialized()` 确认初始化状态
- 在析构时确保调用 `Touch_Up`

## 迁移指南

### 原有代码替换

**原有代码：**
```cpp
Bot_Touch_Action_SLOT(InPut, 8, 1000, botTouch.beginPointer);
Bot_Touch_Move_SLOT(InPut, 8, botTouch.nowPointer);
Bot_Touch_CLOSE_SLOT(InPut, 8);
```

**新代码：**
```cpp
Bot_Touch_Action_SLOT(8, 1000, botTouch.beginPointer);
Bot_Touch_Move_SLOT(8, botTouch.nowPointer);
Bot_Touch_CLOSE_SLOT(8);
```

### 初始化简化

**原有代码：**
```cpp
touchUnput UnPut(_touch_information, _resolution_information);
// 复杂的uinput初始化过程...
```

**新代码：**
```cpp
kernelTouch.Touch_Init(width, height);
```

## 支持的内核版本

- 支持内核版本：4.9 ~ 6.6
- 自动适配不同Android版本
- 兼容QGKI、GKI2.0+架构

## 故障排除

### 1. 初始化失败
- 检查内核版本兼容性
- 确认权限设置
- 验证屏幕分辨率参数

### 2. 触摸无响应
- 确认已调用 `Touch_Init`
- 检查坐标是否在有效范围内
- 验证触摸按下/抬起配对

### 3. 卡屏问题
- 确保每个 `Touch_Down` 都有对应的 `Touch_Up`
- 在程序退出前调用 `Touch_Up`
- 检查是否有异常中断导致触摸点未释放
