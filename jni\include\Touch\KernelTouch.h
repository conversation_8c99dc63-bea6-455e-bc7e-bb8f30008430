#ifndef KERNEL_TOUCH_H
#define KERNEL_TOUCH_H

#include "driver.h"
#include "VecTool.h"
#include <chrono>
#include <thread>

// 内核触摸控制类，用于替代原有的模拟触摸系统
class KernelTouch {
private:
    Driver* driver = nullptr;                    // 内核驱动实例
    bool isInitialized = false;                  // 初始化状态
    bool isTouchDown = false;                    // 触摸按下状态
    int screenWidth = 0;                         // 屏幕宽度
    int screenHeight = 0;                        // 屏幕高度
    VecTor2 lastTouchPos = {0, 0};              // 最后触摸位置
    
public:
    KernelTouch() = default;
    
    ~KernelTouch() {
        if (driver) {
            // 确保触摸抬起
            if (isTouchDown) {
                Touch_Up(0);
            }
            delete driver;
            driver = nullptr;
        }
    }
    
    // 初始化内核触摸系统
    bool Touch_Init(int width, int height) {
        if (isInitialized) {
            return true;
        }
        
        try {
            driver = new Driver();
            if (!driver) {
                return false;
            }
            
            // 初始化内核触摸设备
            if (!driver->uinput_init(width, height)) {
                delete driver;
                driver = nullptr;
                return false;
            }
            
            screenWidth = width;
            screenHeight = height;
            isInitialized = true;
            isTouchDown = false;
            
            return true;
        } catch (...) {
            if (driver) {
                delete driver;
                driver = nullptr;
            }
            return false;
        }
    }
    
    // 触摸按下
    void Touch_Down(int id, int x, int y) {
        if (!isInitialized || !driver) {
            return;
        }
        
        // 确保坐标在有效范围内
        x = std::max(1, std::min(x, screenWidth - 1));
        y = std::max(1, std::min(y, screenHeight - 1));
        
        driver->uinput_down(x, y);
        isTouchDown = true;
        lastTouchPos.x = x;
        lastTouchPos.y = y;
    }
    
    // 触摸移动
    void Touch_Move(int id, int x, int y) {
        if (!isInitialized || !driver || !isTouchDown) {
            return;
        }
        
        // 确保坐标在有效范围内
        x = std::max(1, std::min(x, screenWidth - 1));
        y = std::max(1, std::min(y, screenHeight - 1));
        
        driver->uinput_move(x, y);
        lastTouchPos.x = x;
        lastTouchPos.y = y;
        
        // 添加延迟防止速度太快导致跳跃
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 触摸抬起
    void Touch_Up(int id) {
        if (!isInitialized || !driver || !isTouchDown) {
            return;
        }
        
        driver->uinput_up();
        isTouchDown = false;
    }
    
    // 获取随机坐标（使用内核的高质量随机数）
    int GetRandomCoordinate(int maxValue) {
        if (!isInitialized || !driver) {
            return maxValue / 2;
        }
        return driver->uinput_rand(maxValue);
    }
    
    // 检查是否已初始化
    bool IsInitialized() const {
        return isInitialized;
    }
    
    // 检查是否正在触摸
    bool IsTouchDown() const {
        return isTouchDown;
    }
    
    // 获取最后触摸位置
    VecTor2 GetLastTouchPosition() const {
        return lastTouchPos;
    }
    
    // 获取屏幕尺寸
    VecTor2 GetScreenSize() const {
        return VecTor2(screenWidth, screenHeight);
    }
    
    // 平滑移动到目标位置（用于自瞄）
    void SmoothMoveTo(int targetX, int targetY, int steps = 5) {
        if (!isInitialized || !driver || !isTouchDown) {
            return;
        }
        
        float deltaX = (targetX - lastTouchPos.x) / (float)steps;
        float deltaY = (targetY - lastTouchPos.y) / (float)steps;
        
        for (int i = 1; i <= steps; i++) {
            int newX = lastTouchPos.x + (int)(deltaX * i);
            int newY = lastTouchPos.y + (int)(deltaY * i);
            
            Touch_Move(0, newX, newY);
            std::this_thread::sleep_for(std::chrono::milliseconds(5));
        }
    }
};

#endif // KERNEL_TOUCH_H
